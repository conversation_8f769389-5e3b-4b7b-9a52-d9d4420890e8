import { useLocation } from 'react-router-dom';
import { Link } from 'react-router-dom';

import { useGlobalContext } from '@contexts/GlobalContext';
import { useLayout } from '@contexts/LayoutContext';
import { _menuTabs } from '@constants/menuTabs';
import logo from "@images/Logo.svg"

import Banner from './Banner';

function SideMenu() {
  const { userType } = useGlobalContext();
  const {
    isMobile,
    isSidebarCollapsed,
    isBurgerMenuOpen,
    closeMobileMenu,
    toggleSidebar
  } = useLayout();

  const location = useLocation();
  const routeName = location.pathname;
  const isGroupMembersPage = routeName === '/members/group';

  const menuTabs = _menuTabs[userType] || _menuTabs.user;

  return (
    <>
      <aside
        className={`bg-white dark:bg-dark-surface p-4 flex flex-col items-center justify-start h-[100vh] transition-all duration-300 ease-in-out relative
                   ${isMobile
                     ? (isBurgerMenuOpen ? 'w-64' : 'w-0 -translate-x-full')
                     : (isSidebarCollapsed ? 'w-20' : 'w-full')
                   }
                   ${isMobile && isBurgerMenuOpen ? 'fixed z-40 shadow-xl dark:shadow-2xl' : ''}
                   ${!isMobile ? 'relative' : ''}
                   border-r-0`}
        style={{
          clipPath: 'none'
        }}
      >

         
        
         {/* Professional Toggle Button with Wave Effect */}
         {!isMobile && (
           <div className={`absolute top-1/2 transform -translate-y-1/2 z-50 ${isSidebarCollapsed ? '-right-1' : 'right-0'}`}>
            {/* Wave Background */}
             <div className="w-12 h-20 bg-gradient-to-r from-gray-100 to-gray-100 dark:from-[#0F172A] dark:to-[#0F172A] rounded-l-full shadow-lg border-t-2 border-b-2 border-gray-200 dark:border-gray-600 relative overflow-hidden">
              {/* Wave Pattern - Hidden in dark mode */}
              <div className="absolute inset-0 bg-gradient-to-br from-gray-600/20 via-gray-500/10 to-transparent rounded-l-full dark:hidden"></div>
            </div>
            
            {/* Toggle Button with Advanced Animations */}
            <button
              onClick={toggleSidebar}
               className="absolute top-1/2 left-1/2 transform -translate-x-1/3 -translate-y-1/2 w-10 h-10 bg-gradient-to-br from-gray-700 to-gray-800 hover:from-gray-800 hover:to-gray-900 dark:from-gray-600 dark:to-gray-700 dark:hover:from-gray-700 dark:hover:to-gray-800 rounded-full shadow-lg border-2 border-white dark:border-gray-800 transition-all duration-500 hover:scale-125 hover:shadow-2xl group"
              title={isSidebarCollapsed ? 'Expand Sidebar' : 'Collapse Sidebar'}
              style={{
                animation: 'floatingButton 3s ease-in-out infinite, buttonGlow 2s ease-in-out infinite alternate'
              }}
            >
              {/* Pulsing Ring Animation */}
              <div className="absolute inset-0 rounded-full border-2 border-white/30 dark:border-gray-400/30 animate-ping"></div>
              <div className="absolute inset-0 rounded-full border border-white/20 dark:border-gray-400/20 animate-pulse"></div>
              
              {/* Rotating Background Gradient */}
              <div className="absolute inset-0 rounded-full bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-cyan-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500 animate-spin" style={{ animationDuration: '4s' }}></div>
              
              <div className="w-full h-full flex items-center justify-center relative z-10">
                {isSidebarCollapsed ? (
                   <svg 
                     className="w-5 h-5 text-white transition-all duration-500 group-hover:scale-125 group-hover:rotate-12" 
                     fill="none" 
                     stroke="currentColor" 
                     viewBox="0 0 24 24"
                     style={{
                       filter: 'drop-shadow(0 0 4px rgba(255,255,255,0.5))'
                     }}
                   >
                     <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M9 5l7 7-7 7" />
                   </svg>
                ) : (
                  <svg 
                    className="w-5 h-5 text-white transition-all duration-500 group-hover:scale-125 group-hover:-rotate-12" 
                    fill="none" 
                    stroke="currentColor" 
                    viewBox="0 0 24 24"
                    style={{
                      filter: 'drop-shadow(0 0 4px rgba(255,255,255,0.5))'
                    }}
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M15 19l-7-7 7-7" />
                  </svg>
                )}
              </div>
                     
              {/* Enhanced Glow Effect */}
              <div className="absolute inset-0 rounded-full bg-gradient-to-br from-blue-400/60 via-purple-400/60 to-cyan-400/60 opacity-0 group-hover:opacity-100 transition-all duration-500 blur-md scale-150"></div>
              <div className="absolute inset-0 rounded-full bg-gradient-to-br from-white/40 to-gray-300/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm"></div>
              
              {/* Shimmer Effect */}
              <div className="absolute inset-0 rounded-full bg-gradient-to-r from-transparent via-white/30 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-700 transform -skew-x-12 translate-x-[-120%] group-hover:translate-x-[120%]"></div>
            </button>
          </div>
        )}
        {/* Conditional rendering for mobile to hide content when menu is closed to prevent overflow issues */}
        {(!isMobile || isBurgerMenuOpen) && (
          <>
            {/* Ultra Professional Logo with Advanced Effects */}
            {(!isSidebarCollapsed || isMobile) && (
              <Link to={`/${userType}/dashboard`} className="w-full group">
                <div className="relative w-full p-6 rounded-3xl bg-gradient-to-br from-white/90 via-gray-50/70 to-white/90 dark:from-gray-800/90 dark:via-gray-700/70 dark:to-gray-800/90 backdrop-blur-xl border border-gray-200/60 dark:border-gray-600/60 shadow-2xl hover:shadow-3xl transition-all duration-700 overflow-hidden -mx-2">
                  
                  {/* Animated Gradient Border with Shine */}
                  <div className="absolute inset-0 rounded-3xl bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-cyan-500/20 opacity-30 group-hover:opacity-100 transition-opacity duration-700 animate-pulse"></div>
                  <div className="absolute inset-[2px] rounded-3xl bg-gradient-to-br from-white/90 via-gray-50/70 to-white/90 dark:from-gray-800/90 dark:via-gray-700/70 dark:to-gray-800/90"></div>
                  
                  {/* Moving Light Border Animation */}
                  <div className="absolute inset-0 rounded-3xl bg-gradient-to-r from-transparent via-white/50 to-transparent opacity-0 animate-pulse" style={{ 
                    animation: 'movingLight 3s ease-in-out infinite',
                    animationDelay: '0s'
                  }}></div>
                  <div className="absolute inset-0 rounded-3xl bg-gradient-to-r from-transparent via-cyan-300/60 to-transparent opacity-0 animate-pulse" style={{ 
                    animation: 'movingLight 4s ease-in-out infinite',
                    animationDelay: '1s'
                  }}></div>
                  <div className="absolute inset-0 rounded-3xl bg-gradient-to-r from-transparent via-blue-300/50 to-transparent opacity-0 animate-pulse" style={{ 
                    animation: 'movingLight 3.5s ease-in-out infinite',
                    animationDelay: '2s'
                  }}></div>
                  
                  {/* CSS Animation Keyframes */}
                  <style>{`
                    @keyframes movingLight {
                      0% {
                        opacity: 0;
                        transform: translateX(-100%) rotate(0deg);
                      }
                      25% {
                        opacity: 1;
                        transform: translateX(0%) rotate(90deg);
                      }
                      50% {
                        opacity: 1;
                        transform: translateX(100%) rotate(180deg);
                      }
                      75% {
                        opacity: 0.5;
                        transform: translateX(200%) rotate(270deg);
                      }
                      100% {
                        opacity: 0;
                        transform: translateX(300%) rotate(360deg);
                      }
                    }
                    
                    /* Smooth Wave Edge Animation */
                    @keyframes waveEdge {
                      0% {
                        clip-path: polygon(0 0, calc(100% - 15px) 0, calc(100% - 10px) 20%, calc(100% - 5px) 40%, calc(100% - 3px) 50%, calc(100% - 5px) 60%, calc(100% - 10px) 80%, calc(100% - 15px) 100%, 0 100%);
                        opacity: 1;
                      }
                      25% {
                        clip-path: polygon(0 0, calc(100% - 13px) 0, calc(100% - 8px) 20%, calc(100% - 3px) 40%, calc(100% - 1px) 50%, calc(100% - 3px) 60%, calc(100% - 8px) 80%, calc(100% - 13px) 100%, 0 100%);
                        opacity: 0.8;
                      }
                      50% {
                        clip-path: polygon(0 0, calc(100% - 17px) 0, calc(100% - 12px) 20%, calc(100% - 7px) 40%, calc(100% - 5px) 50%, calc(100% - 7px) 60%, calc(100% - 12px) 80%, calc(100% - 17px) 100%, 0 100%);
                        opacity: 1;
                      }
                      75% {
                        clip-path: polygon(0 0, calc(100% - 11px) 0, calc(100% - 6px) 20%, calc(100% - 1px) 40%, 100% 50%, calc(100% - 1px) 60%, calc(100% - 6px) 80%, calc(100% - 11px) 100%, 0 100%);
                        opacity: 0.9;
                      }
                      100% {
                        clip-path: polygon(0 0, calc(100% - 15px) 0, calc(100% - 10px) 20%, calc(100% - 5px) 40%, calc(100% - 3px) 50%, calc(100% - 5px) 60%, calc(100% - 10px) 80%, calc(100% - 15px) 100%, 0 100%);
                        opacity: 1;
                      }
                    }
                    
                    /* Enhanced Wave Glow Animation */
                    @keyframes waveGlow {
                      0% {
                        box-shadow: 0 0 1px rgba(16, 185, 129, 0.1), inset -1px 0 2px rgba(59, 130, 246, 0.02);
                      }
                      50% {
                        box-shadow: 0 0 2px rgba(16, 185, 129, 0.15), inset -1px 0 3px rgba(59, 130, 246, 0.04);
                      }
                      100% {
                        box-shadow: 0 0 1px rgba(16, 185, 129, 0.1), inset -1px 0 2px rgba(59, 130, 246, 0.02);
                      }
                    }
                    
                    /* Floating Button Animation */
                    @keyframes floatingButton {
                      0%, 100% {
                        transform: translate(-33%, -50%) translateY(0px) rotate(0deg);
                      }
                      25% {
                        transform: translate(-33%, -50%) translateY(-2px) rotate(1deg);
                      }
                      50% {
                        transform: translate(-33%, -50%) translateY(-1px) rotate(0deg);
                      }
                      75% {
                        transform: translate(-33%, -50%) translateY(-3px) rotate(-1deg);
                      }
                    }
                    
                    /* Button Glow Animation */
                    @keyframes buttonGlow {
                      0% {
                        box-shadow: 0 0 5px rgba(59, 130, 246, 0.3), 0 0 10px rgba(59, 130, 246, 0.2), 0 0 15px rgba(59, 130, 246, 0.1);
                      }
                      50% {
                        box-shadow: 0 0 10px rgba(59, 130, 246, 0.5), 0 0 20px rgba(59, 130, 246, 0.3), 0 0 30px rgba(59, 130, 246, 0.2);
                      }
                      100% {
                        box-shadow: 0 0 5px rgba(59, 130, 246, 0.3), 0 0 10px rgba(59, 130, 246, 0.2), 0 0 15px rgba(59, 130, 246, 0.1);
                      }
                    }
                    
                    /* Pulse Ring Animation */
                    @keyframes pulseRing {
                      0% {
                        transform: scale(1);
                        opacity: 1;
                      }
                      100% {
                        transform: scale(1.5);
                        opacity: 0;
                      }
                    }
                    
                    /* Shimmer Effect Animation */
                    @keyframes shimmer {
                      0% {
                        transform: translateX(-100%) skewX(-12deg);
                        opacity: 0;
                      }
                      50% {
                        opacity: 1;
                      }
                      100% {
                        transform: translateX(200%) skewX(-12deg);
                        opacity: 0;
                      }
                    }
                  `}</style>
                  
                  {/* Shine Effect on Border */}
                  <div className="absolute inset-0 rounded-3xl bg-gradient-to-r from-transparent via-white/40 to-transparent -skew-x-12 opacity-0 group-hover:opacity-100 transition-all duration-1000 transform translate-x-[-120%] group-hover:translate-x-[120%]"></div>
                  
                  {/* Logo Container */}
                  <div className="relative z-20 flex justify-center items-center">
                    <img 
                      src={logo} 
                      alt="Ink Null Logo" 
                        className="w-full max-w-44 h-auto transition-all duration-700 filter drop-shadow-lg dark:brightness-0 dark:invert dark:contrast-100 dark:hue-rotate-180" 
                    />
                  </div>
                </div>
              </Link>
            )}

            <ul className={`w-full ${(!isSidebarCollapsed || isMobile) ? 'mt-10 mx-0 my-5' : 'mt-16 m-2'}`}>
              {menuTabs.map((tab, index) => {
                let isActive = false;
                if (isGroupMembersPage) {
                  isActive = tab.activeKey === 'groups';
                } else {
                  isActive = routeName.includes(tab.activeKey);
                }

                return !tab.onlyAdmin || (tab.onlyAdmin && userType === "admin") ? (
                  <Link to={tab.route} key={index} onClick={isMobile ? closeMobileMenu : undefined}> {/* Close menu on item click on mobile */}
                    <li
                      className={`flex p-1 items-center transition-all duration-500 rounded-3xl relative overflow-hidden group ${
                        isActive 
                          ? "bg-gradient-to-br from-white/95 via-gray-50/80 to-white/95 dark:from-gray-800/95 dark:via-gray-700/80 dark:to-gray-800/95 backdrop-blur-xl border-2 border-emerald-300/80 dark:border-blue-300/80 shadow-[0_20px_40px_rgba(0,0,0,0.15)] dark:shadow-[0_20px_40px_rgba(59,130,246,0.3)] hover:shadow-[0_25px_50px_rgba(0,0,0,0.2)] dark:hover:shadow-[0_25px_50px_rgba(59,130,246,0.4)] transform hover:scale-[1.02] transition-all duration-300" 
                          : "text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 hover:bg-gray-100/50 dark:hover:bg-gray-700/30 hover:shadow-md rounded-xl"
                      } ${
                        !isMobile && isSidebarCollapsed ? 'justify-center mb-4 relative group' : ''
                      }`}
                      data-title={tab?.title}
                    >
                      {/* Active Tab Effects - Similar to Logo */}
                      {isActive && (
                        <>
                          {/* Animated Gradient Border */}
                          <div className="absolute inset-0 rounded-3xl bg-gradient-to-r from-emerald-500/20 via-teal-500/20 to-cyan-500/20 dark:from-blue-500/20 dark:via-purple-500/20 dark:to-cyan-500/20 opacity-30 group-hover:opacity-100 transition-opacity duration-700 animate-pulse"></div>
                          <div className="absolute inset-[2px] rounded-3xl bg-gradient-to-br from-white/90 via-gray-50/70 to-white/90 dark:from-gray-800/90 dark:via-gray-700/70 dark:to-gray-800/90"></div>
                          
                          {/* Static Light Border with Gradient - Full Coverage */}
                          <div className="absolute inset-0 rounded-3xl bg-gradient-to-br from-emerald-400/30 via-emerald-300/20 to-emerald-500/30 opacity-40"></div>
                          
                          {/* Dark Mode Static Light with Gradient - Full Coverage */}
                          <div className="absolute inset-0 rounded-3xl bg-gradient-to-br from-blue-300/50 via-blue-200/40 to-blue-400/50 opacity-60 dark:block hidden"></div>
                        </>
                      )}
                      
                      <span className={`relative z-20 ${
                        !isMobile && isSidebarCollapsed
                          ? 'mx-auto'
                          : (isMobile && !isBurgerMenuOpen ? 'mr-0' : 'm-3')
                      } ${isActive ? 'text-emerald-700 dark:text-blue-300 drop-shadow-lg' : ''}`}> {/* Adjust icon margin based on state */}
                        {tab.icon}
                      </span>
                      {((!isMobile && !isSidebarCollapsed) || (isMobile && isBurgerMenuOpen)) && ( // Show title based on state
                         <span className={`menu-item-title relative z-20 ${isActive ? 'text-emerald-700 dark:text-blue-300 font-bold drop-shadow-md' : ''}`}>{tab?.title}</span>
                      )}

                      {/* Tooltip for collapsed state */}
                      {!isMobile && isSidebarCollapsed && (
                        <div className="absolute left-full top-1/2 transform -translate-y-1/2 ml-2 px-2 py-1 bg-gray-800 dark:bg-gray-700 text-white text-sm rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50 shadow-lg">
                          {tab?.title}
                          <div className="absolute right-full top-1/2 transform -translate-y-1/2 border-4 border-transparent border-r-gray-800 dark:border-r-gray-700"></div>
                        </div>
                      )}
                    </li>
                  </Link>
                ) : null;
              })}
            </ul>


            {/* Banner - hide when collapsed on desktop */}
            {(!isSidebarCollapsed || isMobile) && (
              <div className='mt-auto w-full'>
                <Banner />
              </div>
            )}
          </>
        )}
      </aside>
      {/* Optional: Overlay for mobile when burger menu is open */}
      {isMobile && isBurgerMenuOpen && (
        <div
          className="fixed inset-0 bg-black dark:bg-gray-900 opacity-50 dark:opacity-70 z-30 md:hidden transition-opacity duration-300"
          onClick={closeMobileMenu}
        ></div>
      )}
    </>
  );
}

export default SideMenu;