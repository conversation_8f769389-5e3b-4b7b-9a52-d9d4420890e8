import SideMenu from './SideMenu'
import FloatingDarkModeToggle from '@components/FloatingDarkModeToggle'
import { useLayout } from '../../contexts/LayoutContext'
import PropTypes from 'prop-types'

function Layout({ children }) {
    const {
        getSidebarClasses,
        getMainContentClasses,
        isMobile,
        isSidebarCollapsed,
        isBurgerMenuOpen
    } = useLayout();

    return (
        <main className='flex h-[100vh] relative'>

            <aside className={`
                ${getSidebarClasses()}
                layout-sidebar
                ${isMobile ? (isBurgerMenuOpen ? 'mobile-open' : '') : (isSidebarCollapsed ? 'sidebar-collapsed' : 'sidebar-expanded')}
            `}>
                <SideMenu />
            </aside>
            <section className={`
                ${getMainContentClasses()}
                layout-main-content
                ${!isMobile && isSidebarCollapsed ? 'layout-main-expanded' : 'layout-main-normal'}
                flex flex-col
            `}>
                {/* <Banner />                                      Disabled Navbar */}

                <div className="w-full h-full overflow-y-auto main-container">
                    <div className="main-content">
                        {children}
                    </div>
                </div>
            </section>
            
            {/* Floating Dark Mode Toggle */}
            <FloatingDarkModeToggle />
        </main>
    )
}

Layout.propTypes = {
    children: PropTypes.node.isRequired
}

export default Layout